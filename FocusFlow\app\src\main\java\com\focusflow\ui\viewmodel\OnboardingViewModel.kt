package com.focusflow.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.focusflow.data.repository.UserPreferencesRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class OnboardingViewModel @Inject constructor(
    private val userPreferencesRepository: UserPreferencesRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(OnboardingUiState())
    val uiState: StateFlow<OnboardingUiState> = _uiState.asStateFlow()

    fun nextStep() {
        val currentStep = _uiState.value.currentStep
        val nextStep = when (currentStep) {
            OnboardingStep.WELCOME -> OnboardingStep.ADHD_FRIENDLY
            OnboardingStep.ADHD_FRIENDLY -> OnboardingStep.GOALS_FINANCIAL
            OnboardingStep.GOALS_FINANCIAL -> OnboardingStep.GOALS_PERSONAL
            OnboardingStep.GOALS_PERSONAL -> OnboardingStep.INCOME_SETUP
            OnboardingStep.INCOME_SETUP -> OnboardingStep.DEBT_SETUP
            OnboardingStep.DEBT_SETUP -> OnboardingStep.BUDGET_SETUP
            OnboardingStep.BUDGET_SETUP -> OnboardingStep.NOTIFICATION_SETUP
            OnboardingStep.NOTIFICATION_SETUP -> OnboardingStep.COMPLETE
            OnboardingStep.COMPLETE -> OnboardingStep.COMPLETE
        }

        _uiState.value = _uiState.value.copy(currentStep = nextStep)
    }

    suspend fun completeOnboarding(): Boolean {
        return try {
            _uiState.value = _uiState.value.copy(isLoading = true)

            val state = _uiState.value

            // Create user preferences with all collected data
            val userPreferences = com.focusflow.data.model.UserPreferences(
                id = 1,
                hasCompletedOnboarding = true,
                budgetPeriod = "weekly",
                weeklyBudget = state.weeklyBudget.toDoubleOrNull() ?: 300.0,
                monthlyBudget = (state.weeklyBudget.toDoubleOrNull() ?: 300.0) * 4.33,
                enableNotifications = state.enableNotifications,
                notificationTime = state.notificationTime,
                notificationsEnabled = state.enableNotifications,
                reminderTime = state.notificationTime,
                primaryGoal = state.selectedFinancialGoals.firstOrNull(),
                theme = "system",
                darkModeEnabled = false,
                fontSize = "medium"
            )

            // Use insertUserPreferences to ensure the record is created
            userPreferencesRepository.insertUserPreferences(userPreferences)

            _uiState.value = _uiState.value.copy(
                isLoading = false,
                hasCompletedOnboarding = true
            )

            true // Success
        } catch (e: Exception) {
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = "Failed to complete onboarding: ${e.message}"
            )
            false // Failure
        }
    }

    fun setBudget(weeklyBudget: Double) {
        viewModelScope.launch {
            try {
                val currentPrefs = userPreferencesRepository.getUserPreferencesSync()
                val updatedPrefs = currentPrefs?.copy(
                    weeklyBudget = weeklyBudget,
                    monthlyBudget = weeklyBudget * 4.33 // Approximate monthly equivalent
                ) ?: com.focusflow.data.model.UserPreferences(
                    id = 1,
                    hasCompletedOnboarding = false,
                    budgetPeriod = "weekly",
                    weeklyBudget = weeklyBudget,
                    monthlyBudget = weeklyBudget * 4.33,
                    enableNotifications = true,
                    notificationTime = "18:00",
                    theme = "system"
                )
                
                userPreferencesRepository.updateUserPreferences(updatedPrefs)
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to set budget: ${e.message}"
                )
            }
        }
    }

    fun setNotificationPreference(enabled: Boolean) {
        viewModelScope.launch {
            try {
                val currentPrefs = userPreferencesRepository.getUserPreferencesSync()
                val updatedPrefs = currentPrefs?.copy(enableNotifications = enabled)
                    ?: com.focusflow.data.model.UserPreferences(
                        id = 1,
                        hasCompletedOnboarding = false,
                        budgetPeriod = "weekly",
                        weeklyBudget = 300.0,
                        monthlyBudget = 1200.0,
                        enableNotifications = enabled,
                        notificationTime = "18:00",
                        theme = "system"
                    )
                
                userPreferencesRepository.updateUserPreferences(updatedPrefs)
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to set notification preference: ${e.message}"
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    fun updateFinancialGoals(goals: List<String>) {
        _uiState.value = _uiState.value.copy(selectedFinancialGoals = goals)
    }

    fun updatePersonalGoals(goals: List<String>) {
        _uiState.value = _uiState.value.copy(selectedPersonalGoals = goals)
    }

    fun updateMonthlyIncome(income: String) {
        _uiState.value = _uiState.value.copy(monthlyIncome = income)
    }

    fun updateHasDebt(hasDebt: Boolean) {
        _uiState.value = _uiState.value.copy(hasDebt = hasDebt)
    }

    fun updateWeeklyBudget(budget: String) {
        _uiState.value = _uiState.value.copy(weeklyBudget = budget)
    }

    fun updateNotificationSettings(enabled: Boolean, time: String) {
        _uiState.value = _uiState.value.copy(
            enableNotifications = enabled,
            notificationTime = time
        )
    }
}

data class OnboardingUiState(
    val currentStep: OnboardingStep = OnboardingStep.WELCOME,
    val isLoading: Boolean = false,
    val error: String? = null,
    val selectedFinancialGoals: List<String> = emptyList(),
    val selectedPersonalGoals: List<String> = emptyList(),
    val monthlyIncome: String = "",
    val hasDebt: Boolean? = null,
    val weeklyBudget: String = "300",
    val enableNotifications: Boolean = true,
    val notificationTime: String = "18:00",
    val hasCompletedOnboarding: Boolean = false
)

enum class OnboardingStep {
    WELCOME,
    ADHD_FRIENDLY,
    GOALS_FINANCIAL,
    GOALS_PERSONAL,
    INCOME_SETUP,
    DEBT_SETUP,
    BUDGET_SETUP,
    NOTIFICATION_SETUP,
    COMPLETE
}


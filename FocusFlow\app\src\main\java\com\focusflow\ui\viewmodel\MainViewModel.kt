package com.focusflow.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.focusflow.data.repository.UserPreferencesRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class MainViewModel @Inject constructor(
    private val userPreferencesRepository: UserPreferencesRepository,
    private val gamificationService: com.focusflow.service.GamificationService
) : ViewModel() {

    private val _uiState = MutableStateFlow(MainUiState())
    val uiState: StateFlow<MainUiState> = _uiState.asStateFlow()

    init {
        checkOnboardingStatus()
        // Initialize gamification system
        viewModelScope.launch {
            try {
                gamificationService.initializeGamification()
            } catch (e: Exception) {
                // Log error but don't block app startup
            }
        }
    }

    fun checkOnboardingStatus() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                val userPreferences = userPreferencesRepository.getUserPreferencesSync()
                val hasCompletedOnboarding = userPreferences?.hasCompletedOnboarding ?: false
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    hasCompletedOnboarding = hasCompletedOnboarding
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    hasCompletedOnboarding = false,
                    error = "Failed to check onboarding status: ${e.message}"
                )
            }
        }
    }
}

data class MainUiState(
    val isLoading: Boolean = true,
    val hasCompletedOnboarding: Boolean = false,
    val error: String? = null
)

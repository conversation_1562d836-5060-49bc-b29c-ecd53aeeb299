package com.focusflow.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.focusflow.data.model.Expense
import com.focusflow.data.repository.ExpenseRepository
import com.focusflow.data.repository.UserPreferencesRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.datetime.*
import javax.inject.Inject

@HiltViewModel
class ExpenseViewModel @Inject constructor(
    private val expenseRepository: ExpenseRepository,
    private val userPreferencesRepository: UserPreferencesRepository,
    private val gamificationService: com.focusflow.service.GamificationService
) : ViewModel() {

    private val _uiState = MutableStateFlow(ExpenseUiState())
    val uiState: StateFlow<ExpenseUiState> = _uiState.asStateFlow()

    val allExpenses = expenseRepository.getAllExpenses()
    val allCategories = expenseRepository.getAllCategories()

    init {
        loadCurrentPeriodExpenses()
    }

    private fun loadCurrentPeriodExpenses() {
        viewModelScope.launch {
            val preferences = userPreferencesRepository.getUserPreferencesSync()
            val period = preferences?.budgetPeriod ?: "weekly"
            
            val (startDate, endDate) = getCurrentPeriodDates(period)
            
            expenseRepository.getExpensesByDateRange(startDate, endDate)
                .collect { expenses ->
                    val total = expenseRepository.getTotalSpentInPeriod(startDate, endDate)
                    _uiState.value = _uiState.value.copy(
                        currentPeriodExpenses = expenses,
                        totalSpentThisPeriod = total,
                        budgetPeriod = period
                    )
                }
        }
    }

    fun addExpense(
        amount: Double,
        category: String,
        description: String,
        merchant: String? = null,
        date: LocalDateTime = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
    ) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                val expense = Expense(
                    amount = amount,
                    category = category,
                    description = description,
                    merchant = merchant,
                    date = date
                )
                
                expenseRepository.insertExpense(expense)

                // Trigger gamification for expense logging
                try {
                    gamificationService.onExpenseLogged()
                } catch (e: Exception) {
                    // Don't fail expense logging if gamification fails
                }

                _uiState.value = _uiState.value.copy(isLoading = false)

                // Refresh current period data
                loadCurrentPeriodExpenses()
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to add expense: ${e.message}"
                )
            }
        }
    }

    fun deleteExpense(expense: Expense) {
        viewModelScope.launch {
            try {
                expenseRepository.deleteExpense(expense)
                loadCurrentPeriodExpenses()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to delete expense: ${e.message}"
                )
            }
        }
    }

    fun updateExpense(expense: Expense) {
        viewModelScope.launch {
            try {
                expenseRepository.updateExpense(expense)
                loadCurrentPeriodExpenses()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to update expense: ${e.message}"
                )
            }
        }
    }

    fun getExpensesByCategory(category: String): Flow<List<Expense>> {
        return expenseRepository.getExpensesByCategory(category)
    }

    fun getExpensesByDateRange(startDate: LocalDateTime, endDate: LocalDateTime): Flow<List<Expense>> {
        return expenseRepository.getExpensesByDateRange(startDate, endDate)
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    private fun getCurrentPeriodDates(period: String): Pair<LocalDateTime, LocalDateTime> {
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        val today = now.date
        
        return when (period) {
            "weekly" -> {
                val startOfWeek = today.minus(today.dayOfWeek.ordinal, DateTimeUnit.DAY)
                val endOfWeek = startOfWeek.plus(6, DateTimeUnit.DAY)
                Pair(
                    startOfWeek.atTime(0, 0),
                    endOfWeek.atTime(23, 59, 59)
                )
            }
            "monthly" -> {
                val startOfMonth = LocalDate(today.year, today.month, 1)
                val endOfMonth = startOfMonth.plus(1, DateTimeUnit.MONTH).minus(1, DateTimeUnit.DAY)
                Pair(
                    startOfMonth.atTime(0, 0),
                    endOfMonth.atTime(23, 59, 59)
                )
            }
            else -> {
                // Default to weekly
                val startOfWeek = today.minus(today.dayOfWeek.ordinal, DateTimeUnit.DAY)
                val endOfWeek = startOfWeek.plus(6, DateTimeUnit.DAY)
                Pair(
                    startOfWeek.atTime(0, 0),
                    endOfWeek.atTime(23, 59, 59)
                )
            }
        }
    }
}

data class ExpenseUiState(
    val currentPeriodExpenses: List<Expense> = emptyList(),
    val totalSpentThisPeriod: Double = 0.0,
    val budgetPeriod: String = "weekly",
    val isLoading: Boolean = false,
    val error: String? = null
)

// Predefined expense categories for ADHD-friendly quick selection
object ExpenseCategories {
    val categories = listOf(
        "Food & Dining",
        "Transportation",
        "Shopping",
        "Entertainment",
        "Bills & Utilities",
        "Healthcare",
        "Groceries",
        "Gas",
        "Coffee & Drinks",
        "Subscriptions",
        "Personal Care",
        "Education",
        "Gifts",
        "Other"
    )
}


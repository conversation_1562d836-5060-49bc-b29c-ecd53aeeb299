package com.focusflow.di;

@dagger.Module
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0007\u001a\u00020\b2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u000b\u001a\u00020\f2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0012\u0010\r\u001a\u00020\u00062\b\b\u0001\u0010\u000e\u001a\u00020\u000fH\u0007J\u0010\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007\u00a8\u0006\u001c"}, d2 = {"Lcom/focusflow/di/DatabaseModule;", "", "()V", "provideAIInteractionDao", "Lcom/focusflow/data/dao/AIInteractionDao;", "database", "Lcom/focusflow/data/database/FocusFlowDatabase;", "provideAchievementDao", "Lcom/focusflow/data/dao/AchievementDao;", "provideBudgetCategoryDao", "Lcom/focusflow/data/dao/BudgetCategoryDao;", "provideCreditCardDao", "Lcom/focusflow/data/dao/CreditCardDao;", "provideDatabase", "context", "Landroid/content/Context;", "provideExpenseDao", "Lcom/focusflow/data/dao/ExpenseDao;", "provideHabitLogDao", "Lcom/focusflow/data/dao/HabitLogDao;", "provideTaskDao", "Lcom/focusflow/data/dao/TaskDao;", "provideUserPreferencesDao", "Lcom/focusflow/data/dao/UserPreferencesDao;", "provideUserStatsDao", "Lcom/focusflow/data/dao/UserStatsDao;", "provideVirtualPetDao", "Lcom/focusflow/data/dao/VirtualPetDao;", "app_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class DatabaseModule {
    @org.jetbrains.annotations.NotNull
    public static final com.focusflow.di.DatabaseModule INSTANCE = null;
    
    private DatabaseModule() {
        super();
    }
    
    @dagger.Provides
    @javax.inject.Singleton
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.database.FocusFlowDatabase provideDatabase(@dagger.hilt.android.qualifiers.ApplicationContext
    @org.jetbrains.annotations.NotNull
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.ExpenseDao provideExpenseDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.CreditCardDao provideCreditCardDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.BudgetCategoryDao provideBudgetCategoryDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.HabitLogDao provideHabitLogDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.TaskDao provideTaskDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.AIInteractionDao provideAIInteractionDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.UserPreferencesDao provideUserPreferencesDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.AchievementDao provideAchievementDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.UserStatsDao provideUserStatsDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.dao.VirtualPetDao provideVirtualPetDao(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database) {
        return null;
    }
}
package com.focusflow.di

import android.content.Context
import androidx.room.Room
import com.focusflow.data.dao.*
import com.focusflow.data.database.FocusFlowDatabase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {

    @Provides
    @Singleton
    fun provideDatabase(@ApplicationContext context: Context): FocusFlowDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            FocusFlowDatabase::class.java,
            "focusflow_database"
        ).build()
    }

    @Provides
    fun provideExpenseDao(database: FocusFlowDatabase): ExpenseDao = database.expenseDao()

    @Provides
    fun provideCreditCardDao(database: FocusFlowDatabase): CreditCardDao = database.creditCardDao()

    @Provides
    fun provideBudgetCategoryDao(database: FocusFlowDatabase): BudgetCategoryDao = database.budgetCategoryDao()

    @Provides
    fun provideHabitLogDao(database: FocusFlowDatabase): HabitLogDao = database.habitLogDao()

    @Provides
    fun provideTaskDao(database: FocusFlowDatabase): TaskDao = database.taskDao()

    @Provides
    fun provideAIInteractionDao(database: FocusFlowDatabase): AIInteractionDao = database.aiInteractionDao()

    @Provides
    fun provideUserPreferencesDao(database: FocusFlowDatabase): UserPreferencesDao = database.userPreferencesDao()

    @Provides
    fun provideAchievementDao(database: FocusFlowDatabase): AchievementDao = database.achievementDao()

    @Provides
    fun provideUserStatsDao(database: FocusFlowDatabase): UserStatsDao = database.userStatsDao()

    @Provides
    fun provideVirtualPetDao(database: FocusFlowDatabase): VirtualPetDao = database.virtualPetDao()
}

